import { useEffect, useRef, useState } from "react";
import { getTaskAttachments, updatePersonalTask, updateProjectTask, uploadTaskAttachment } from "../../api/taskManagement";
import { getProjectMembers, transformUserData } from "../../api/userManagement";
import addIcon from "../../assets/add.svg";
import closeIcon from "../../assets/closePanel.svg";
import startDateIcon from "../../assets/creationdate.svg";
import endDateIcon from "../../assets/enddate.svg";
import fileTextIcon from "../../assets/file-text.svg";
import loadFileIcon from "../../assets/loadfile.svg";
import "../../styles/JobCreate.css";
import MemberAddPopup from "./MemberAddPopup";

// Custom Select Dropdown Component
const CustomSelect = ({
  options,
  value,
  onChange,
  placeholder,
  disabled,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const selectedOption = options.find((option) => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-select" ref={dropdownRef}>
      <div
        className={`job-form-group select ${error ? "error" : ""}`}
        onClick={toggleDropdown}
        style={{
          position: "relative",
          cursor: disabled ? "not-allowed" : "pointer",
          opacity: disabled ? 0.7 : 1,
        }}
      >
        <div
          className="select-selected"
          style={{
            padding: "6px 8px",
            border: "1px solid #bdbdbd",
            borderRadius: "4px",
            fontSize: "13px",
            fontWeight: "500",
            backgroundColor: "#fff",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: "32px",
            boxSizing: "border-box",
          }}
        >
          <span style={{ color: selectedOption ? "#5B5B5B" : "#999" }}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ marginLeft: "8px" }}
          >
            <path
              d="M6 9L12 15L18 9"
              stroke="#666666"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {isOpen && (
        <div className="job-dropdown">
          {options.map((option) => (
            <div
              key={option.value}
              className="job-dropdown-item"
              onClick={() => handleSelect(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Utility functions
const formatDate = (dateString) => {
  if (!dateString) return '';

  // Handle different date formats
  let date;
  if (typeof dateString === 'string') {
    const cleanDateString = dateString.trim();

    // Check if it's Vietnamese format (dd/mm/yyyy)
    if (cleanDateString.includes('/')) {
      const parts = cleanDateString.split('/');
      if (parts.length === 3) {
        // Assume it's dd/mm/yyyy format
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);

        // Create date object (month is 0-indexed in JavaScript)
        date = new Date(year, month - 1, day);
      } else {
        date = new Date(cleanDateString);
      }
    } else {
      date = new Date(cleanDateString);
    }
  } else if (dateString instanceof Date) {
    date = dateString;
  } else {
    return '';
  }

  // Check if date is valid
  if (isNaN(date.getTime())) {
    console.warn('Invalid date value:', dateString);
    return '';
  }

  // Return in yyyy-mm-dd format for input[type="date"]
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

const extractTaskName = (fullName) => {
  if (!fullName) return '';

  // Remove task ID prefix like "TASK-2E6A38 " from task name
  // This regex ensures we only match at the beginning and capture everything after the first space
  const match = fullName.match(/^TASK-[A-Z0-9]+\s+(.+)$/);
  if (match) {
    let extractedName = match[1].trim();

    // Double check: if the extracted name still starts with TASK-, extract again
    const doubleMatch = extractedName.match(/^TASK-[A-Z0-9]+\s+(.+)$/);
    if (doubleMatch) {
      extractedName = doubleMatch[1].trim();
    }

    return extractedName;
  }

  // Fallback for old format "PRJ-123: "
  const oldMatch = fullName.match(/^PRJ-\d+:\s*(.+)$/);
  if (oldMatch) {
    return oldMatch[1].trim();
  }

  // If no pattern matches, return the full name (might be just the task name without ID)
  return fullName.trim();
};

const getTaskId = (fullName) => {
  if (!fullName) return '';
  // Extract task ID like "TASK-2E6A38" from full name
  const match = fullName.match(/^(TASK-[A-Z0-9]+)\s+.+$/);
  return match ? match[1] : '';
};

const cleanTaskName = (name) => {
  if (!name) return '';

  let cleanName = name.trim();

  // Remove duplicate TASK- patterns like "TASK-123 TASK-123 Name"
  const duplicateMatch = cleanName.match(/^(TASK-[A-Z0-9]+)\s+\1\s+(.+)$/);
  if (duplicateMatch) {
    cleanName = `${duplicateMatch[1]} ${duplicateMatch[2]}`;
  }

  return cleanName;
};

const reconstructFullTaskName = (taskId, editableName) => {
  if (!taskId || !editableName) return editableName || '';

  // Clean the editable name first - remove any existing task IDs
  let cleanName = editableName.trim();

  // Remove any existing TASK-XXXXX pattern from the beginning
  const cleanMatch = cleanName.match(/^TASK-[A-Z0-9]+\s+(.+)$/);
  if (cleanMatch) {
    cleanName = cleanMatch[1].trim();
  }

  // Check if the clean name already starts with the expected task ID
  if (cleanName.startsWith(taskId)) {
    return cleanName; // Already properly formatted
  }

  // Construct the full name with the task ID and clean it
  const fullName = `${taskId} ${cleanName}`;
  return cleanTaskName(fullName);
};

const getUserAvatar = (user) => {
  if (!user) return "https://randomuser.me/api/portraits/men/1.jpg";
  return user.avatar || user.profilePicture || "https://randomuser.me/api/portraits/men/1.jpg";
};

const JobUpdate = ({
  task,
  onCancel,
  onSave,
  disableDepartmentFilter = false,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "medium", // Changed from "normal" to "medium" since normal is not available
    status: "waiting",
    members: [],
    attachments: [],
  });
  const [originalTaskId, setOriginalTaskId] = useState("");
  const [showMemberPopup, setShowMemberPopup] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "",
    status: "",
  });
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [existingAttachments, setExistingAttachments] = useState([]);
  const [loadingAttachments, setLoadingAttachments] = useState(false);

  const fileInputRef = useRef(null);

  // Fetch existing attachments
  const fetchExistingAttachments = async () => {
    if (!task || !task.projectId) return;

    setLoadingAttachments(true);
    try {
      const taskId = task.id || task._id;
      const response = await getTaskAttachments(task.projectId, taskId);
      const attachments = response.data || response || [];

      // Transform to match the format expected by the UI
      const transformedAttachments = attachments.map((att) => ({
        _id: att._id,
        name: att.fileName,
        size: "N/A",
        type: att.fileName.split(".").pop().toLowerCase(),
        date: new Date(att.uploadedAt).toLocaleDateString("vi-VN"),
        url: att.fileUrl,
        uploadedBy: att.uploadedBy,
        isExisting: true // Flag to distinguish from new files
      }));

      setExistingAttachments(transformedAttachments);
    } catch (error) {
      console.error("Error fetching attachments:", error);
      setExistingAttachments([]);
    }
    setLoadingAttachments(false);
  };

  // Initialize form data when task changes
  useEffect(() => {
    if (task) {
      console.log('JobUpdate - Task data:', task);
      console.log('JobUpdate - startDate:', task.startDate);
      console.log('JobUpdate - endDate:', task.endDate);
      console.log('JobUpdate - dueDate:', task.dueDate);
      console.log('JobUpdate - status:', task.status);
      console.log('JobUpdate - priority:', task.priority);

      const originalName = task.name || task.title || '';
      const extractedName = extractTaskName(originalName);
      const taskId = getTaskId(originalName);

      const formattedStartDate = formatDate(task.startDate);
      const formattedEndDate = formatDate(task.dueDate || task.endDate);

      console.log('JobUpdate - original name:', originalName);
      console.log('JobUpdate - extracted name:', extractedName);
      console.log('JobUpdate - task ID:', taskId);
      console.log('JobUpdate - formatted startDate:', formattedStartDate);
      console.log('JobUpdate - formatted endDate:', formattedEndDate);

      // Map priority to available options (remove 'normal' if not in options)
      let mappedPriority = task.priority || 'medium';
      if (mappedPriority === 'normal') {
        mappedPriority = 'medium'; // Default to medium if normal is not available
      }

      // Map backend status to frontend status
      let mappedStatus = task.status || 'pending';
      const statusMapping = {
        'pending': 'waiting',
        'waiting': 'waiting',
        'in_progress': 'in_progress',
        'review': 'review',
        'completed': 'completed',
        'overdue': 'overdue'
      };

      // Use mapping if available, otherwise keep original
      mappedStatus = statusMapping[mappedStatus] || mappedStatus;

      // Ensure status is valid for frontend
      const validStatuses = ['waiting', 'in_progress', 'review', 'completed', 'overdue'];
      if (!validStatuses.includes(mappedStatus)) {
        mappedStatus = 'waiting'; // Default to waiting if status is invalid
      }

      console.log('JobUpdate - mapped priority:', mappedPriority);
      console.log('JobUpdate - mapped status:', mappedStatus);

      setOriginalTaskId(taskId);
      setFormData({
        name: extractedName,
        description: task.description || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        priority: mappedPriority,
        status: mappedStatus,
        members: task.assignee ? task.assignee.map(a => ({
          id: a.userId || a.id,
          name: a.name || (a.user && a.user.name) || 'Không có tên',
          avatar: getUserAvatar(a)
        })) : [],
        attachments: []
      });
      setFieldErrors({});

      // Fetch existing attachments
      fetchExistingAttachments();
    }
  }, [task]);

  // Fetch project members when opening member popup
  useEffect(() => {
    if (showMemberPopup && task?.projectId && users.length === 0) {
      setLoadingUsers(true);
      getProjectMembers(task.projectId)
        .then((response) => {
          const rawUsers = response.data || response || [];
          const transformedUsers = rawUsers.map(member => {
            if (member.user) {
              // Nếu member có nested user object
              return transformUserData(member.user);
            } else {
              // Nếu member đã là user object
              return transformUserData(member);
            }
          });
          setUsers(transformedUsers);
        })
        .catch((err) => {
          console.error('Error fetching project members:', err);
          setUsers([]);
        })
        .finally(() => setLoadingUsers(false));
    }
  }, [showMemberPopup, task?.projectId, users.length]);

  // Reset users when popup closes
  useEffect(() => {
    if (!showMemberPopup) {
      setUsers([]);
    }
  }, [showMemberPopup]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name?.trim()) {
      errors.name = 'Tên công việc không được để trống';
    }

    if (!formData.description?.trim()) {
      errors.description = 'Mô tả không được để trống';
    }

    if (!formData.startDate) {
      errors.startDate = 'Vui lòng chọn ngày bắt đầu';
    }

    if (!formData.endDate) {
      errors.endDate = 'Vui lòng chọn ngày kết thúc';
    }

    if (formData.startDate && formData.endDate &&
        new Date(formData.startDate) > new Date(formData.endDate)) {
      errors.endDate = 'Ngày kết thúc phải sau ngày bắt đầu';
    }

    if (!formData.priority) {
      errors.priority = 'Vui lòng chọn mức độ ưu tiên';
    }

    if (!formData.status) {
      errors.status = 'Vui lòng chọn trạng thái';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError("");
      
      const assignedToIds = formData.members.map(member => member.id).filter(Boolean);

      // Safely create date objects
      const startDate = formData.startDate ? new Date(formData.startDate) : null;
      const endDate = formData.endDate ? new Date(formData.endDate) : null;

      // Validate dates
      if (startDate && isNaN(startDate.getTime())) {
        setError("Ngày bắt đầu không hợp lệ");
        return;
      }
      if (endDate && isNaN(endDate.getTime())) {
        setError("Ngày kết thúc không hợp lệ");
        return;
      }

      // IMPORTANT: Only send the pure task name (without ID) to backend
      // Backend will handle the ID part automatically
      const pureTaskName = formData.name.trim();

      console.log('JobUpdate - Sending pure task name to backend:');
      console.log('  - originalTaskId:', originalTaskId);
      console.log('  - formData.name (pure):', pureTaskName);
      console.log('  - Will NOT reconstruct full name for backend');

      // Map frontend status back to backend status
      const frontendToBackendStatus = {
        'waiting': 'pending',
        'in_progress': 'in_progress',
        'review': 'review',
        'completed': 'completed',
        'overdue': 'overdue'
      };

      const backendStatus = frontendToBackendStatus[formData.status] || formData.status;

      const updateData = {
        name: pureTaskName, // Send only pure task name to backend
        description: formData.description.trim(),
        startDate: startDate ? startDate.toISOString() : null,
        dueDate: endDate ? endDate.toISOString() : null,
        priority: formData.priority,
        status: backendStatus,
        assignee: assignedToIds
      };

      // Call API update based on task type
      if (task.projectId) {
        // Project task
        await updateProjectTask(task.projectId, task.id || task._id, updateData);
      } else {
        // Personal task
        await updatePersonalTask(task.id || task._id, updateData);
      }

      // Upload new attachments if any
      let hasUploadedFiles = false;
      if (formData.attachments && formData.attachments.length > 0 && task.projectId) {
        const taskId = task.id || task._id;
        for (const file of formData.attachments) {
          try {
            await uploadTaskAttachment(task.projectId, taskId, file);
            hasUploadedFiles = true;
          } catch (error) {
            console.error(`Error uploading file ${file.name}:`, error);
            // Vẫn coi như upload thành công vì có thể là lỗi CORS nhưng file đã lên server
            hasUploadedFiles = true;
          }
        }
      }

      // Trigger refresh data in parent components
      window.dispatchEvent(new Event("projectTasksUpdated"));
      window.dispatchEvent(new Event("personalTasksUpdated"));
      window.dispatchEvent(new Event("tasksUpdated"));

      // Trigger attachments refresh if files were uploaded
      if (hasUploadedFiles) {
        window.dispatchEvent(new Event("taskAttachmentsUpdated"));
      }

      if (onSave) {
        // Create updated task object for immediate UI update
        // Reconstruct the display name with original task ID for UI consistency
        const displayName = originalTaskId ? `${originalTaskId} ${pureTaskName}` : pureTaskName;

        const updatedTaskForUI = {
          ...task, // Keep all original task properties
          id: task.id || task._id,
          name: displayName, // Display name with ID for UI
          description: formData.description.trim(),
          startDate: formData.startDate ?
            new Date(formData.startDate).toLocaleDateString("vi-VN") :
            task.startDate,
          endDate: formData.endDate ?
            new Date(formData.endDate).toLocaleDateString("vi-VN") :
            task.endDate,
          dueDate: formData.endDate ?
            new Date(formData.endDate).toLocaleDateString("vi-VN") :
            task.dueDate,
          priority: formData.priority,
          status: backendStatus, // Use backend status for consistency
          assignee: task.assignee, // Keep existing assignee for now
          projectId: task.projectId,
          // Additional data for parent components
          updateData: updateData
        };

        console.log('JobUpdate - Pure name sent to backend:', pureTaskName);
        console.log('JobUpdate - Display name for UI:', displayName);
        console.log('JobUpdate - Final task for UI:', updatedTaskForUI);
        onSave(updatedTaskForUI);
      }

      // Reset form attachments after successful save
      setFormData(prev => ({
        ...prev,
        attachments: []
      }));

      // Refresh existing attachments
      fetchExistingAttachments();

      // Close the edit modal
      onCancel();

    } catch (error) {
      console.error("Error updating task:", error);
      setError("Có lỗi xảy ra khi cập nhật công việc");
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = () => {
    setShowMemberPopup(true);
  };

  const handleMemberAdded = (member) => {
    setFormData((prev) => ({
      ...prev,
      members: [...prev.members, member],
    }));
  };

  const handleRemoveMember = (indexToRemove) => {
    setFormData((prev) => ({
      ...prev,
      members: prev.members.filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleAddFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...files],
    }));
    // Reset input to allow selecting same file again
    e.target.value = "";
  };

  const handleRemoveFile = (indexToRemove) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter(
        (_, index) => index !== indexToRemove
      ),
    }));
  };

  // Priority options for CustomSelect
  const priorityOptions = [
    { value: "low", label: "Thấp" },
    { value: "medium", label: "Trung bình" },
    { value: "high", label: "Cao" },
  ];

  // Status options for CustomSelect
  const statusOptions = [
    { value: "waiting", label: "Đang chờ" },
    { value: "in_progress", label: "Đang triển khai" },
    { value: "review", label: "Xem xét" },
    { value: "completed", label: "Hoàn thành" },
    { value: "overdue", label: "Quá hạn" },
  ];

  if (!task) return null;

  return (
    <div className="job-create-panel-backdrop" onClick={onCancel}>
      <div className="job-create-panel-container" onClick={e => e.stopPropagation()}>
        <div className="job-create-panel-header">
          <span className="job-create-panel-title">Chỉnh sửa công việc</span>
          <button className="job-panel-close-btn" onClick={onCancel}>
            <img src={closeIcon} alt="Đóng" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="job-create-panel-form">
          {error && (
            <div className="error-message-panel">
              {error}
            </div>
          )}
          <div className="job-panel-section">
            {/* Tên công việc */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Tên công việc</div>
                <div className="job-panel-value">
                  <input
                    type="text"
                    placeholder="Nhập tên công việc"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className={`job-create-input ${fieldErrors.name ? "error" : ""}`}
                  />
                </div>
              </div>
              {fieldErrors.name && (
                <div className="job-create-field-error">
                  {fieldErrors.name}
                </div>
              )}
            </div>

            {/* Thành viên */}
            <div className="job-panel-row">
              <div className="job-panel-label">Thành viên</div>
              <div className="job-panel-value job-panel-members-flex-row">
                <button type="button" className="job-panel-add-member-btn" onClick={handleAddMember}>
                  <img src={addIcon} alt="Thêm thành viên" />
                </button>
                <div className="job-panel-members-list">
                  {formData.members.map((member, index) => (
                    <span key={index} className="job-panel-member">
                      <img src={member.avatar} alt={member.name} className="job-panel-avatar" />
                      <button type="button" className="job-panel-remove-member-btn" onClick={() => handleRemoveMember(index)} title="Xóa thành viên">×</button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Mức độ ưu tiên */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Mức độ ưu tiên</div>
                <div className="job-panel-value">
                  <CustomSelect
                    options={priorityOptions}
                    value={formData.priority}
                    onChange={(value) => handleInputChange("priority", value)}
                    placeholder="Chọn mức độ ưu tiên"
                    error={fieldErrors.priority}
                  />
                </div>
              </div>
              {fieldErrors.priority && (
                <div className="job-create-field-error">
                  {fieldErrors.priority}
                </div>
              )}
            </div>

            {/* Trạng thái */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Trạng thái</div>
                <div className="job-panel-value">
                  <CustomSelect
                    options={statusOptions}
                    value={formData.status}
                    onChange={(value) => handleInputChange("status", value)}
                    placeholder="Chọn trạng thái"
                    error={fieldErrors.status}
                  />
                </div>
              </div>
              {fieldErrors.status && (
                <div className="job-create-field-error">
                  {fieldErrors.status}
                </div>
              )}
            </div>

            {/* Ngày bắt đầu */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Ngày bắt đầu</div>
                <div className="job-panel-value">
                  <div className={`job-create-date-input-group-custom ${fieldErrors.startDate ? "error" : ""}`} style={{width: '100%'}}>
                    <img src={startDateIcon} alt="calendar" className="job-calendar-icon-custom" />
                    <input
                      name="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => handleInputChange("startDate", e.target.value)}
                      placeholder="Chọn ngày bắt đầu"
                      className="job-create-date-custom"
                    />
                  </div>
                </div>
              </div>
              {fieldErrors.startDate && (
                <div className="job-create-field-error">
                  {fieldErrors.startDate}
                </div>
              )}
            </div>

            {/* Ngày kết thúc */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Ngày kết thúc</div>
                <div className="job-panel-value">
                  <div className={`job-create-date-input-group-custom ${fieldErrors.endDate ? "error" : ""}`} style={{width: '100%'}}>
                    <img src={endDateIcon} alt="calendar" className="job-calendar-icon-custom" />
                    <input
                      name="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => handleInputChange("endDate", e.target.value)}
                      placeholder="Chọn ngày kết thúc"
                      className="job-create-date-custom"
                    />
                  </div>
                </div>
              </div>
              {fieldErrors.endDate && (
                <div className="job-create-field-error">
                  {fieldErrors.endDate}
                </div>
              )}
            </div>

            {/* Mô tả */}
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Mô tả</div>
                <div className="job-panel-value">
                  <textarea
                    placeholder="Nhập mô tả công việc"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    rows={3}
                    className={`job-create-textarea ${fieldErrors.description ? "error" : ""}`}
                  />
                </div>
              </div>
              {fieldErrors.description && (
                <div className="job-create-field-error">
                  {fieldErrors.description}
                </div>
              )}
            </div>

            {/* Tệp đính kèm */}
            <div className="job-panel-rows">
              <div className="job-panel-label">Tệp đính kèm</div>
              <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
                <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    style={{ display: "none" }}
                    ref={fileInputRef}
                  />
                  <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                  <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
                </div>
                {/* Hiển thị tệp đính kèm hiện có */}
                {existingAttachments && existingAttachments.length > 0 && (
                  <div className="job-panel-file-list" style={{width: '100%'}}>
                    <div style={{fontSize: '12px', color: '#666', marginBottom: '8px'}}>Tệp đính kèm hiện có:</div>
                    {existingAttachments.map((file, idx) => (
                      <div className="job-panel-file-item" key={`existing-${idx}`}>
                        <img src={fileTextIcon} alt="file" className="job-panel-file-icon" />
                        <span className="job-panel-file-name">{file.name}</span>
                        <span style={{fontSize: '11px', color: '#999'}}>({file.date})</span>
                      </div>
                    ))}
                  </div>
                )}
                {/* Hiển thị tệp mới được thêm */}
                {formData.attachments && formData.attachments.length > 0 && (
                  <div className="job-panel-file-list" style={{width: '100%'}}>
                    <div style={{fontSize: '12px', color: '#666', marginBottom: '8px'}}>Tệp mới thêm:</div>
                    {formData.attachments.map((file, idx) => (
                      <div className="job-panel-file-item" key={`new-${idx}`}>
                        <img src={fileTextIcon} alt="file" className="job-panel-file-icon" />
                        <span className="job-panel-file-name">{file.name}</span>
                        <button type="button" className="job-panel-remove-file-btn" onClick={() => handleRemoveFile(idx)} title="Xóa tệp">×</button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
        <div className="job-create-panel-footer">
          <button type="button" className="job-panel-cancel-btn" onClick={onCancel}>Huỷ</button>
          <button type="submit" className="job-panel-create-btn" onClick={handleSubmit} disabled={loading}>
            {loading ? "Đang lưu..." : "Lưu thay đổi"}
          </button>
        </div>
        <MemberAddPopup
          isOpen={showMemberPopup}
          onClose={() => setShowMemberPopup(false)}
          onAddMember={handleMemberAdded}
          existingMembers={formData.members}
          users={users}
          loadingUsers={loadingUsers}
          disableDepartmentFilter={disableDepartmentFilter}
        />
      </div>
    </div>
  );
};

export default JobUpdate;
