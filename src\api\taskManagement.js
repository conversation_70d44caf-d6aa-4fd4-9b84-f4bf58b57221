import { API_URL, getCurrentUserEndpoints } from "./endpoints.js";

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem("token");
    const authToken = localStorage.getItem("authToken");
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: "Có lỗi xảy ra" };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || "Có lỗi xảy ra");
  }
  return response.json();
};

// Lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  return getCurrentUserEndpoints();
};

// ========== TASK MANAGEMENT FUNCTIONS ==========

// Lấy thống kê trạng thái công việc cho charts
export async function getTaskStatusStats(projectId = null) {
  try {
    // Luôn tính toán từ danh sách tasks vì backend chưa có endpoint /stats
    const tasks = projectId
      ? await getProjectTasks(projectId)
      : await getMyProjectTasks();

    const taskList = tasks.data || tasks;

    if (!Array.isArray(taskList)) {
      return {
        data: {
          pending: 0,
          in_progress: 0,
          completed: 0,
          overdue: 0,
          review: 0,
        },
      };
    }

    // Tính toán stats từ dữ liệu thực tế
    const stats = {
      pending: taskList.filter((t) => t.status === "pending").length,
      in_progress: taskList.filter((t) => t.status === "in_progress").length,
      completed: taskList.filter((t) => t.status === "completed").length,
      overdue: taskList.filter((t) => t.status === "overdue").length,
      review: taskList.filter((t) => t.status === "review").length,
    };

    return { data: stats };
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Thêm user vào project members
export async function addUserToProject(projectId, userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.ADD_PROJECT_MEMBER?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền thêm thành viên vào dự án");
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify({ userId }),
    });

    return await handleResponse(response);
  } catch (err) {
    throw err;
  }
}

// Lấy thông tin project và members cho Charts
export async function getProjectInfo(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const projectUrl = endpoints.GET_PROJECT?.(projectId);
    const membersUrl = `${API_URL}/api/users/projects/${projectId}/members`;

    if (!projectUrl) {
      throw new Error("Không có quyền truy cập thông tin dự án");
    }

    const [projectResponse, membersResponse, tasksResponse] = await Promise.all(
      [
        fetch(projectUrl, {
          method: "GET",
          headers: getAuthHeaders(),
        }),
        fetch(membersUrl, {
          method: "GET",
          headers: getAuthHeaders(),
        }),
        // Lấy thêm dữ liệu tasks để tính toán hiệu suất
        getProjectTasks(projectId).catch(() => ({ data: [] })),
      ]
    );

    const project = await handleResponse(projectResponse);
    const members = await handleResponse(membersResponse);
    const tasks = tasksResponse.data || tasksResponse || [];

    // Transform members data for Charts component
    const transformedMembers = (members.data || members || []).map((member) => {
      const user = member.user || member;
      const userId = user.id || user._id;

      // Tính toán tasks của user này
      const userTasks = tasks.filter((task) => {
        // Backend populate assignedToId thành assignedToId object
        if (task.assignedToId) {
          const assignedId =
            task.assignedToId._id || task.assignedToId.id || task.assignedToId;
          const userIdStr = userId.toString();
          const assignedIdStr = assignedId.toString();
          return assignedIdStr === userIdStr;
        }
        return false;
      });

      const totalTasks = userTasks.length;
      const completedTasks = userTasks.filter(
        (task) => task.status === "completed"
      ).length;
      const efficiency =
        totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      return {
        id: userId,
        name: user.fullName || user.name,
        email: user.email,
        avatar: user.avatar,
        department: user.departmentInfo?.name || user.department || "IT",
        position: user.position || user.role,
        tasks: {
          waiting: userTasks.filter((task) => task.status === "pending").length,
          inProgress: userTasks.filter((task) => task.status === "in_progress")
            .length,
          completed: completedTasks,
          review: userTasks.filter((task) => task.status === "review").length,
        },
        efficiency,
        totalTasks,
      };
    });

    return {
      project: project.data || project,
      members: transformedMembers,
    };
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách công việc của một dự án
export async function getProjectTasks(projectId, params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PROJECT_TASKS?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc của dự án");
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    const result = await handleResponse(response);
    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Tạo công việc mới trong dự án
export async function createProjectTask(projectId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.CREATE_PROJECT_TASK?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền tạo công việc");
    }

    // Chuẩn bị dữ liệu task theo format API
    const formattedData = {
      title: taskData.name || taskData.title,
      description: taskData.description || "",
      priority: taskData.priority || "normal",
      status: taskData.status || "waiting",
      dueDate: taskData.dueDate,
      startDate: taskData.startDate,
      parentTaskId: taskData.parentTaskId || null,
    };

    // Hỗ trợ cả assignedToIds (array) và assignedToId (single) để backward compatibility
    if (taskData.assignedToIds && Array.isArray(taskData.assignedToIds)) {
      formattedData.assignedToIds = taskData.assignedToIds;
      formattedData.assignedToId = taskData.assignedToIds[0]; // backward compatibility
    } else if (taskData.assignedToId) {
      formattedData.assignedToId = taskData.assignedToId;
      formattedData.assignedToIds = [taskData.assignedToId];
    } else if (taskData.assignee?.[0]?.userId) {
      formattedData.assignedToId = taskData.assignee[0].userId;
      formattedData.assignedToIds = [taskData.assignee[0].userId];
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(formattedData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Cập nhật công việc
export async function updateProjectTask(projectId, taskId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền cập nhật công việc");
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData = {};

    if (taskData.name !== undefined) updateData.title = taskData.name;
    if (taskData.description !== undefined)
      updateData.description = taskData.description;
    if (taskData.status !== undefined) updateData.status = taskData.status;
    if (taskData.priority !== undefined)
      updateData.priority = taskData.priority;
    if (taskData.dueDate !== undefined) updateData.dueDate = taskData.dueDate;
    if (taskData.startDate !== undefined)
      updateData.startDate = taskData.startDate;
    if (taskData.assignee !== undefined) {
      updateData.assignedToId =
        taskData.assignee?.[0]?.userId || taskData.assignee;
    }
    if (taskData.progress !== undefined)
      updateData.progress = taskData.progress;

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa công việc
export async function deleteProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa công việc");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Khôi phục công việc đã xóa
export async function restoreProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.RESTORE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền khôi phục công việc");
    }

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa vĩnh viễn công việc
export async function permanentDeleteProjectTask(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PERMANENT_DELETE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa vĩnh viễn công việc");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách công việc đã xóa
export async function getDeletedProjectTasks(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETED_TASKS?.(projectId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc đã xóa");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy thống kê công việc
export async function getTaskStats(taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = `${
      endpoints.TASKS || "/api/tasks"
    }/${taskId}/stats/overview`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy tất cả project tasks mà user được assign (cho trang My Job)
export async function getMyProjectTasks(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.MY_TASKS;

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc được giao");
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== PERSONAL TASK FUNCTIONS ==========

// Lấy công việc cá nhân
export async function getPersonalTasks(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PERSONAL_TASKS;

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc cá nhân");
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `${apiUrl}${queryString ? `?${queryString}` : ""}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Tạo công việc cá nhân
export async function createPersonalTask(taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.CREATE_PERSONAL_TASK;

    if (!apiUrl) {
      throw new Error("Không có quyền tạo công việc cá nhân");
    }

    const formattedData = {
      title: taskData.name || taskData.title,
      description: taskData.description || "",
      priority: taskData.priority || "normal",
      status: taskData.status || "waiting",
      dueDate: taskData.dueDate,
      startDate: taskData.startDate,
    };

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(formattedData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Cập nhật công việc cá nhân
export async function updatePersonalTask(taskId, taskData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_PERSONAL_TASK?.(taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền cập nhật công việc cá nhân");
    }

    const updateData = {};

    if (taskData.name !== undefined) updateData.title = taskData.name;
    if (taskData.description !== undefined)
      updateData.description = taskData.description;
    if (taskData.status !== undefined) updateData.status = taskData.status;
    if (taskData.priority !== undefined)
      updateData.priority = taskData.priority;
    if (taskData.dueDate !== undefined) updateData.dueDate = taskData.dueDate;
    if (taskData.startDate !== undefined)
      updateData.startDate = taskData.startDate;
    if (taskData.progress !== undefined)
      updateData.progress = taskData.progress;

    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify(updateData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa công việc cá nhân
export async function deletePersonalTask(taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_PERSONAL_TASK?.(taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền xóa công việc cá nhân");
    }

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách công việc cá nhân đã xóa (thùng rác)
export async function getDeletedPersonalTasks() {
  try {
    const response = await fetch("/api/common/personal-tasks/trashed", {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Khôi phục công việc cá nhân đã xóa
export async function restorePersonalTask(taskId) {
  try {
    const response = await fetch(
      `/api/staff/personal-tasks/${taskId}/restore`,
      {
        method: "POST",
        headers: getAuthHeaders(),
      }
    );
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa vĩnh viễn công việc cá nhân
export async function permanentDeletePersonalTask(taskId) {
  try {
    const response = await fetch(
      `/api/common/personal-tasks/${taskId}/permanent`,
      {
        method: "DELETE",
        headers: getAuthHeaders(),
      }
    );
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== UTILITY FUNCTIONS ==========

// Transform dữ liệu task từ backend về format frontend
export const transformTaskData = (backendTask) => {
  // Xử lý nhiều assignees
  let assignees = [];

  // Ưu tiên assignedToMultiple (array) nếu có
  if (backendTask.assignedToMultiple && Array.isArray(backendTask.assignedToMultiple)) {
    assignees = backendTask.assignedToMultiple.map(user => ({
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }));
  }
  // Fallback về assignedTo hoặc assignedToId (single user)
  else if (backendTask.assignedTo || backendTask.assignedToId) {
    const user = backendTask.assignedTo || backendTask.assignedToId;
    assignees = [{
      userId: user._id || user.id,
      name: user.fullName || user.name || "Chưa có tên",
      avatar: user.avatar || user.profilePicture || "",
    }];
  }

  const transformedTask = {
    id: backendTask._id || backendTask.id || `TSK-${Date.now()}`,
    name: `${backendTask.taskCode || `TASK-${Date.now()}`} ${
      backendTask.title || "Chưa có tên"
    }`,
    status: backendTask.status || "pending",
    priority: backendTask.priority || "medium",
    assignee: assignees, // Array of assignees
    description: backendTask.description || "",
    dueDate: backendTask.dueDate
      ? new Date(backendTask.dueDate).toLocaleDateString("vi-VN")
      : null,
    startDate: backendTask.startDate
      ? new Date(backendTask.startDate).toLocaleDateString("vi-VN")
      : null,
    creator:
      backendTask.createdBy || backendTask.createdById
        ? {
            name:
              (backendTask.createdBy || backendTask.createdById).fullName ||
              (backendTask.createdBy || backendTask.createdById).name ||
              "Chưa có tên",
            avatar:
              (backendTask.createdBy || backendTask.createdById).avatar || "",
          }
        : { name: "System", avatar: "" },
    attachments: backendTask.attachments || [],
    activities: backendTask.activities || [],
    children: backendTask.subtasks || [],
    progress: backendTask.progress || 0,
    code: backendTask.code || backendTask._id || `TSK-${Date.now()}`,
    comments: Array.isArray(backendTask.comments)
      ? backendTask.comments.length
      : 0,
    // Add project information
    projectId: (backendTask.projectId && backendTask.projectId._id) || backendTask.projectId || backendTask.project_id,
    projectCode: (backendTask.projectId && backendTask.projectId.projectCode) || 
                 backendTask.projectCode || 
                 (backendTask.project && (backendTask.project.projectCode || backendTask.project.code)) ||
                 'PRJ-N/A',
    project: backendTask.projectId || backendTask.project || null,
  };

  return transformedTask;
};

// Transform dữ liệu task list từ backend
export const transformTaskListData = (backendTasks) => {
  if (!Array.isArray(backendTasks)) {
    return [];
  }

  return backendTasks.map((task) => transformTaskData(task));
};

// Kiểm tra quyền của user hiện tại với tasks
export const checkTaskPermissions = () => {
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const role = user.role || user.user?.role || "staff";

  return {
    canViewTasks: true, // Tất cả user đều có thể xem tasks
    canCreateTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    canEditTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    canDeleteTasks: ["admin", "ceo", "hr", "leader"].includes(role),
    canAssignTasks: ["admin", "ceo", "hr", "leader", "departmenthead"].includes(
      role
    ),
    role: role,
  };
};

// Lấy task theo ID
export async function getTaskById(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_TASK?.(projectId, taskId);

    if (!apiUrl) {
      throw new Error("Không có quyền truy cập công việc");
    }

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách bình luận của task
export async function getTaskComments(projectId, taskId) {
  try {
    const endpoints = getCurrentEndpoints();
    const url = endpoints.UPDATE_TASK?.(projectId, taskId) + "/comments";
    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Gửi bình luận mới cho task
export async function postTaskComment(projectId, taskId, content) {
  try {
    const endpoints = getCurrentEndpoints();
    const url = endpoints.UPDATE_TASK?.(projectId, taskId) + "/comments";
    const response = await fetch(url, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify({ content }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== TASK ATTACHMENTS FUNCTIONS ==========

// Upload file đính kèm cho task
export async function uploadTaskAttachment(projectId, taskId, file) {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const token = getAuthToken();
    const url = `${API_URL}/api/projects/${projectId}/tasks/${taskId}/attachments`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Không set Content-Type để browser tự động set với boundary cho FormData
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Lấy danh sách file đính kèm của task
export async function getTaskAttachments(projectId, taskId) {
  try {
    const url = `${API_URL}/api/projects/${projectId}/tasks/${taskId}/attachments`;
    const response = await fetch(url, {
      method: "GET",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// Xóa file đính kèm khỏi task
export async function deleteTaskAttachment(projectId, taskId, attachmentId) {
  try {
    const url = `${API_URL}/api/projects/${projectId}/tasks/${taskId}/attachments/${attachmentId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === "Failed to fetch") {
      throw new Error("Không thể kết nối tới máy chủ.");
    }
    throw err;
  }
}

// ========== TEST FUNCTIONS ==========
