// Thêm cache và preload ở đầu file
let profileCache = null;
let profileCacheTimestamp = 0;
const PROFILE_CACHE_DURATION = 10000; // 10 giây
let profilePreloadPromise = null;

import React, { useState, useEffect } from "react";
import "../../styles/Profile.css";
import user1 from "../../assets/user1.png";
import userRoundIcon from "../../assets/user-round.svg";
import closePanelIcon from "../../assets/closePanel.svg";
import positionIcon from "../../assets/position.svg";
import departmentIcon from "../../assets/department.svg";
import locationIcon from "../../assets/location.svg";
import lockIcon from "../../assets/lock.svg";
import cameraIcon from "../../assets/camera.svg";
import eyeIcon from "../../assets/eye.svg";
import eyeOffIcon from "../../assets/eye-off.svg";
import { getProfile, updateProfile, updateAvatar, changePassword, getRoleDisplayName } from "../../api/profile";
import { validateChangePasswordForm } from "../../utils/validation";

const Profile = ({ onClose }) => {
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [editForm, setEditForm] = useState({
    fullName: '',
    email: '',
    position: ''
  });
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });
  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordTouched, setPasswordTouched] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });
  const [successMessage, setSuccessMessage] = useState('');

  // Preload function
  const preloadProfile = async () => {
    if (profilePreloadPromise) return profilePreloadPromise;
    profilePreloadPromise = (async () => {
      try {
        const response = await getProfile();
        let userData = null;
        if (response && response.user) {
          userData = response.user;
        } else if (response && response.data) {
          userData = response.data;
        } else if (response && response.email) {
          userData = response;
        }
        profileCache = userData;
        profileCacheTimestamp = Date.now();
        return userData;
      } catch {
        return null;
      }
    })();
    return profilePreloadPromise;
  };

  // Fetch profile data on component mount
  useEffect(() => {
    const now = Date.now();
    if (profileCache && (now - profileCacheTimestamp) < PROFILE_CACHE_DURATION) {
      setProfileData(profileCache);
      setLoading(false);
      return;
    }
    setLoading(true);
    if (profilePreloadPromise) {
      profilePreloadPromise.then((userData) => {
        setProfileData(userData);
        setLoading(false);
      });
    } else {
      fetchProfileData();
    }
  }, []);

  useEffect(() => { preloadProfile(); }, []);

  const fetchProfileData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getProfile();
      let userData = null;
      if (response && response.user) {
        userData = response.user;
      } else if (response && response.data) {
        userData = response.data;
      } else if (response && response.email) {
        userData = response;
      }
      if (userData) {
        setProfileData(userData);
        setEditForm({
          fullName: userData.fullName || '',
          email: userData.email || '',
          position: userData.position || ''
        });
      } else {
        setProfileData(null); // ensure profileData is null on error
        setError('Không lấy được thông tin hồ sơ! (API trả về không đúng định dạng)');
      }
    } catch (err) {
      setProfileData(null); // ensure profileData is null on error
      // Handle specific authentication errors
      if (err.message.includes('401') || err.message.includes('Unauthorized') || err.message.includes('token')) {
        setError('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        setLoading(true);
        setError(null);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setAvatarPreview(e.target.result);
        };
        reader.readAsDataURL(file);

        // Upload avatar to server
        const response = await updateAvatar(file);
        if (response.success) {
          // Lấy avatar mới từ response (có thể là response.avatar hoặc response.data.avatar)
          const newAvatar = response.avatar || (response.data && response.data.avatar);
          setProfileData(prev => ({
            ...prev,
            avatar: newAvatar
          }));
          setSuccessMessage('Cập nhật avatar thành công!');
          setTimeout(() => setSuccessMessage(''), 3000);
          setAvatarPreview(null); // Xóa preview để hiển thị avatar mới từ API
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const response = await updateProfile(editForm);
      if (response.success) {
        setProfileData(prev => ({
          ...prev,
          ...editForm
        }));
        setIsEditing(false);
        setSuccessMessage('Cập nhật thông tin thành công!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();

    // Validate form using custom validation
    const validationErrors = validateChangePasswordForm({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
      confirmPassword: passwordForm.confirmPassword
    });

    setPasswordErrors(validationErrors);

    // Check if there are any validation errors
    if (validationErrors.currentPassword || validationErrors.newPassword || validationErrors.confirmPassword) {
      setError('Vui lòng kiểm tra lại thông tin nhập vào.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      });
      
      if (response.success) {
        setIsChangingPassword(false);
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setShowPassword({
          currentPassword: false,
          newPassword: false,
          confirmPassword: false
        });
        setPasswordErrors({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setPasswordTouched({
          currentPassword: false,
          newPassword: false,
          confirmPassword: false
        });
        setSuccessMessage('Đổi mật khẩu thành công!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordInputChange = (e) => {
    const { name, value } = e.target;
    const updatedForm = {
      ...passwordForm,
      [name]: value
    };

    setPasswordForm(updatedForm);

    // Mark field as touched
    setPasswordTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Perform realtime validation
    const validationErrors = validateChangePasswordForm({
      currentPassword: updatedForm.currentPassword,
      newPassword: updatedForm.newPassword,
      confirmPassword: updatedForm.confirmPassword
    });

    setPasswordErrors(validationErrors);
  };

  const togglePasswordVisibility = (fieldName) => {
    setShowPassword(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };



  const startEditing = () => {
    setIsEditing(true);
    setEditForm({
      fullName: profileData?.fullName || '',
      email: profileData?.email || '',
      position: profileData?.position || ''
    });
  };

  const cancelEditing = () => {
    setIsEditing(false);
    setError(null);
  };

  const cancelPasswordChange = () => {
    setIsChangingPassword(false);
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setShowPassword({
      currentPassword: false,
      newPassword: false,
      confirmPassword: false
    });
    setPasswordErrors({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordTouched({
      currentPassword: false,
      newPassword: false,
      confirmPassword: false
    });
    setError(null);
  };

  const isValidAvatar = (avatar) =>
    typeof avatar === 'string' &&
    avatar.trim() !== '' &&
    (avatar.startsWith('http://') || avatar.startsWith('https://'));

  if (loading) {
    return (
      <aside className="profile-sidepanel">
        <div className="profile-header">
          <span className="profile-title">Hồ sơ cá nhân</span>
        </div>
        <div className="profile-content">
          <div className="profile-user-section">
            <div className="profile-avatar-wrapper">
              <div style={{ width: 80, height: 80, borderRadius: '50%', background: '#f0f0f0', margin: '0 auto', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
            </div>
            <div className="profile-user-info">
              <div style={{ width: 120, height: 18, background: '#f0f0f0', borderRadius: 4, margin: '8px auto', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
              <div style={{ width: 180, height: 14, background: '#f0f0f0', borderRadius: 4, margin: '8px auto', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
            </div>
          </div>
          <div className="profile-info-section">
            {[1,2,3].map(i => (
              <div key={i} className="profile-info-item">
                <div className="profile-info-icon" style={{ width: 24, height: 24, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                <div className="profile-info-content">
                  <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  <div style={{ width: 120, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <style>{`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}</style>
      </aside>
    );
  }

  if (error) {
    const isAuthError = error.includes('token') || error.includes('Unauthorized') || error.includes('hết hạn');

    return (
      <aside className="profile-sidepanel">
        <div className="profile-header">
          <span className="profile-title">Hồ sơ cá nhân</span>
        </div>
        <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
          Lỗi: {error}
          <div style={{ marginTop: '15px' }}>
            <button
              onClick={fetchProfileData}
              style={{
                display: 'block',
                margin: '10px auto',
                padding: '5px 10px',
                background: '#4CAF50',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Thử lại
            </button>
            {isAuthError && (
              <button
                onClick={() => {
                  localStorage.clear();
                  window.location.href = '/login';
                }}
                style={{
                  display: 'block',
                  margin: '10px auto',
                  padding: '5px 10px',
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Đăng nhập lại
              </button>
            )}
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className="profile-sidepanel">
      <div className="profile-header">
        <div className="profile-header-content">
          <div className="profile-title-section">
            <div className="profile-title-row">
              <img src={userRoundIcon} alt="user" className="profile-title-icon" />
              <span className="profile-title">Hồ sơ cá nhân</span>
            </div>
            <span className="profile-subtitle">Quản lí thông tin của bạn</span>
          </div>
          <img
            src={closePanelIcon}
            alt="close"
            className="profile-close-icon"
            onClick={onClose}
          />
        </div>
      </div>

      <div className="profile-content">

        {successMessage && (
        <div style={{
          padding: '10px',
          margin: '10px',
          background: '#4CAF50',
          color: 'white',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          {successMessage}
        </div>
      )}

      {error && (
        <div style={{
          padding: '10px',
          margin: '10px',
          background: '#f44336',
          color: 'white',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          {error}
        </div>
      )}

      {/* Only render avatar and user info if profileData is loaded */}
      {profileData && (
        <div className="profile-user-section">
          <div className="profile-avatar-wrapper">
            <img
              src={avatarPreview || (profileData && isValidAvatar(profileData.avatar) ? profileData.avatar : user1)}
              alt="avatar"
              className="profile-avatar"
            />
          </div>
          <div className="profile-user-info">
            <div className="profile-name">
              {profileData.fullName || 'Chưa có tên'}
            </div>
            <div className="profile-email">
              {profileData?.email || ''}
            </div>
          </div>
        </div>
      )}

      {/* Only render profile form if profileData is loaded */}
      {profileData && (
        isChangingPassword ? (
          <form className="profile-form" onSubmit={handlePasswordChange} noValidate>
            <div className="profile-form-group">
              <label>Mật khẩu hiện tại</label>
              <div className="password-input-wrapper">
                <input
                  type={showPassword.currentPassword ? "text" : "password"}
                  name="currentPassword"
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordInputChange}
                  className={passwordErrors.currentPassword ? 'error' : ''}
                  placeholder="Nhập mật khẩu hiện tại"
                />
                <img
                  src={showPassword.currentPassword ? eyeOffIcon : eyeIcon}
                  alt="toggle password visibility"
                  className="password-toggle-icon"
                  onClick={() => togglePasswordVisibility('currentPassword')}
                />
              </div>
              {passwordTouched.currentPassword && passwordErrors.currentPassword && (
                <div className="profile-validation-error">{passwordErrors.currentPassword}</div>
              )}
            </div>
            <div className="profile-form-group">
              <label>Mật khẩu mới</label>
              <div className="password-input-wrapper">
                <input
                  type={showPassword.newPassword ? "text" : "password"}
                  name="newPassword"
                  value={passwordForm.newPassword}
                  onChange={handlePasswordInputChange}
                  className={passwordErrors.newPassword ? 'error' : ''}
                  placeholder="Nhập mật khẩu mới"
                />
                <img
                  src={showPassword.newPassword ? eyeOffIcon : eyeIcon}
                  alt="toggle password visibility"
                  className="password-toggle-icon"
                  onClick={() => togglePasswordVisibility('newPassword')}
                />
              </div>
              {passwordTouched.newPassword && passwordErrors.newPassword && (
                <div className="profile-validation-error">{passwordErrors.newPassword}</div>
              )}
            </div>
            <div className="profile-form-group">
              <label>Xác nhận mật khẩu mới</label>
              <div className="password-input-wrapper">
                <input
                  type={showPassword.confirmPassword ? "text" : "password"}
                  name="confirmPassword"
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordInputChange}
                  className={passwordErrors.confirmPassword ? 'error' : ''}
                  placeholder="Nhập lại mật khẩu mới"
                />
                <img
                  src={showPassword.confirmPassword ? eyeOffIcon : eyeIcon}
                  alt="toggle password visibility"
                  className="password-toggle-icon"
                  onClick={() => togglePasswordVisibility('confirmPassword')}
                />
              </div>
              {passwordTouched.confirmPassword && passwordErrors.confirmPassword && (
                <div className="profile-validation-error">{passwordErrors.confirmPassword}</div>
              )}
            </div>
            <div className="profile-password-form-buttons">
              
              <button
                type="button"
                onClick={cancelPasswordChange}
                className="profile-password-cancel-btn"
              >
                Hủy
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="profile-password-submit-btn"
              >
                {loading ? 'Đang xử lý...' : 'Xác nhận'}
              </button>
            </div>
          </form>
        ) : isEditing ? (
          <form className="profile-form" onSubmit={handleEditSubmit}>
            <div className="profile-form-group">
              <label>Họ và tên</label>
              <input
                type="text"
                name="fullName"
                value={editForm.fullName}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="profile-form-group">
              <label>Email</label>
              <input
                type="email"
                name="email"
                value={editForm.email}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="profile-form-group">
              <label>Vị trí</label>
              <input
                type="text"
                name="position"
                value={editForm.position}
                onChange={handleInputChange}
              />
            </div>
            <div className="profile-edit-form-buttons">
              <button
                type="submit"
                disabled={loading}
                className="profile-edit-submit-btn"
              >
                {loading ? 'Đang xử lý...' : 'Lưu thay đổi'}
              </button>
              <button
                type="button"
                onClick={cancelEditing}
                className="profile-edit-cancel-btn"
              >
                Hủy
              </button>
            </div>
          </form>
        ) : (
          <div className="profile-info-section">
            <div className="profile-info-item">
              <img src={positionIcon} alt="position" className="profile-info-icon" />
              <div className="profile-info-content">
                <span className="profile-info-label">Chức vụ</span>
                <div className="profile-info-value">
                  {getRoleDisplayName(profileData?.role) || 'Nhân viên'}
                </div>
              </div>
            </div>

            <div className="profile-info-item">
              <img src={departmentIcon} alt="department" className="profile-info-icon" />
              <div className="profile-info-content">
                <span className="profile-info-label">Phòng ban</span>
                <div className="profile-info-value">
                  {(profileData && (profileData.departmentId?.name || profileData.department?.name)) || 'Phòng IT'}
                </div>
              </div>
            </div>

            <div className="profile-info-item">
              <img src={locationIcon} alt="location" className="profile-info-icon" />
              <div className="profile-info-content">
                <span className="profile-info-label">Vị trí</span>
                <div className="profile-info-value">
                  {profileData?.position || 'Admin System'}
                </div>
              </div>
            </div>
          </div>
        )
      )}

      {/* Action buttons at bottom */}
      {!isEditing && !isChangingPassword && (
        <div className="profile-action-buttons">
          <button
            onClick={() => setIsChangingPassword(true)}
            className="profile-password-btn"
          >
            <img src={lockIcon} alt="lock" className="btn-icon" />
            Đổi mật khẩu
          </button>
          <label className="profile-update-avatar-btn">
            <input
              type="file"
              style={{ display: "none" }}
              accept="image/*"
              onChange={handleAvatarChange}
            />
            <img src={cameraIcon} alt="camera" className="btn-icon" />
            Cập nhật ảnh
          </label>
        </div>
      )}
      </div>
    </aside>
  );
};

export default Profile;
