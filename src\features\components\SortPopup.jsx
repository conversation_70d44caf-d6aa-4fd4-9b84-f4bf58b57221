import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import '../../styles/SortPopup.css';
// Import các icon sử dụng trong sort popup
import arrowBottomIcon from '../../assets/arrowbottom.svg';
import arrowTopIcon from '../../assets/arrowtop.svg';
import recentlyIcon from '../../assets/recently.svg';
import startDateIcon from '../../assets/creationdate.svg';

const SortPopup = ({ sortBy, onSortOptionSelect, onClose }) => {
  // Đóng popup khi click ra ngoài và tính toán vị trí
  useEffect(() => {
    // Tính toán vị trí popup
    const updatePopupPosition = () => {
      const popup = document.getElementById('sort-popup');
      const btn = document.querySelector('.btn-sort');
      if (popup && btn) {
        const btnRect = btn.getBoundingClientRect();
        const popupWidth = 280; // Width của popup

        // Đặt popup bên dướ<PERSON> n<PERSON>, căn phải
        popup.style.position = 'fixed';
        popup.style.top = `${btnRect.bottom + 8}px`;
        popup.style.left = `${btnRect.right - popupWidth}px`;
        popup.style.zIndex = '999999';

        // Đảm bảo popup không bị tràn ra ngoài màn hình
        const popupRect = popup.getBoundingClientRect();
        if (popupRect.right > window.innerWidth) {
          popup.style.left = `${window.innerWidth - popupWidth - 10}px`;
        }
        if (popupRect.left < 0) {
          popup.style.left = '10px';
        }
      }
    };

    // Cập nhật vị trí ngay lập tức và khi resize/scroll
    const timeoutId = setTimeout(updatePopupPosition, 10);
    window.addEventListener('resize', updatePopupPosition);
    window.addEventListener('scroll', updatePopupPosition, true);

    const handleClickOutside = (event) => {
      const popup = document.getElementById('sort-popup');
      const btn = document.querySelector('.btn-sort');

      // Đóng popup nếu click ra ngoài popup (nhưng không phải nút)
      if (popup && !popup.contains(event.target) && btn && !btn.contains(event.target)) {
        onClose();
      }
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('resize', updatePopupPosition);
      window.removeEventListener('scroll', updatePopupPosition, true);
    };
  }, [onClose]);

  return createPortal(
    <div className="sort-popup custom-sort-popup" id="sort-popup">
      <div className="custom-sort-title">Sắp xếp</div>

      {/* Header hiển thị trạng thái sắp xếp hiện tại */}
      <div className="sort-status-header">
        <span className="sort-status-label">Đang sắp xếp theo:</span>
        <span className="sort-status-value">
          {sortBy === 'name-asc' && 'A→Z'}
          {sortBy === 'name-desc' && 'Z→A'}
          {sortBy === 'create-newest' && 'Gần đây'}
          {sortBy === 'create-oldest' && 'Cũ nhất'}
          {!sortBy && 'Chưa chọn'}
        </span>
      </div>

      <div className="custom-sort-options">
        {/* Các tuỳ chọn sắp xếp */}
        <div className={`custom-sort-option ${sortBy === 'name-asc' ? 'selected' : ''}`} onClick={() => onSortOptionSelect('name-asc')}>
          <div className="sort-option-content">
            <img src={arrowBottomIcon} alt="A→Z" className="sort-icon" />
            <span className="sort-label">A→Z</span>
          </div>
          <label className="ios-switch"
            onClick={(e) => { e.stopPropagation(); onSortOptionSelect('name-asc'); }}
            onMouseDown={(e) => { e.stopPropagation(); onSortOptionSelect('name-asc'); }}>
            <input type="checkbox" checked={sortBy === 'name-asc'} onChange={() => {}} />
            <span className="slider" onClick={(e) => { e.stopPropagation(); onSortOptionSelect('name-asc'); }}></span>
          </label>
        </div>

        <div className={`custom-sort-option ${sortBy === 'name-desc' ? 'selected' : ''}`} onClick={() => onSortOptionSelect('name-desc')}>
          <div className="sort-option-content">
            <img src={arrowTopIcon} alt="Z→A" className="sort-icon" />
            <span className="sort-label">Z→A</span>
          </div>
          <label className="ios-switch"
            onClick={(e) => { e.stopPropagation(); onSortOptionSelect('name-desc'); }}
            onMouseDown={(e) => { e.stopPropagation(); onSortOptionSelect('name-desc'); }}>
            <input type="checkbox" checked={sortBy === 'name-desc'} onChange={() => {}} />
            <span className="slider" onClick={(e) => { e.stopPropagation(); onSortOptionSelect('name-desc'); }}></span>
          </label>
        </div>

        <div className={`custom-sort-option ${sortBy === 'create-newest' ? 'selected' : ''}`} onClick={() => onSortOptionSelect('create-newest')}>
          <div className="sort-option-content">
            <img src={recentlyIcon} alt="Gần đây" className="sort-icon" />
            <span className="sort-label">Gần đây</span>
          </div>
          <label className="ios-switch"
            onClick={(e) => { e.stopPropagation(); onSortOptionSelect('create-newest'); }}
            onMouseDown={(e) => { e.stopPropagation(); onSortOptionSelect('create-newest'); }}>
            <input type="checkbox" checked={sortBy === 'create-newest'} onChange={() => {}} />
            <span className="slider" onClick={(e) => { e.stopPropagation(); onSortOptionSelect('create-newest'); }}></span>
          </label>
        </div>

        <div className={`custom-sort-option ${sortBy === 'create-oldest' ? 'selected' : ''}`} onClick={() => onSortOptionSelect('create-oldest')}>
          <div className="sort-option-content">
            <img src={startDateIcon} alt="Cũ nhất" className="sort-icon" />
            <span className="sort-label">Cũ nhất</span>
          </div>
          <label className="ios-switch"
            onClick={(e) => { e.stopPropagation(); onSortOptionSelect('create-oldest'); }}
            onMouseDown={(e) => { e.stopPropagation(); onSortOptionSelect('create-oldest'); }}>
            <input type="checkbox" checked={sortBy === 'create-oldest'} onChange={() => {}} />
            <span className="slider" onClick={(e) => { e.stopPropagation(); onSortOptionSelect('create-oldest'); }}></span>
          </label>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default SortPopup;
